#!/usr/bin/env python3
"""
Browser installer for Oops I Stole Your Sheet
This script installs Playwright browsers when running as an executable
"""

import os
import sys
import subprocess
from pathlib import Path


def install_browsers():
    """Install Playwright browsers"""
    print("🌐 Installing Playwright browsers...")
    print("This may take a few minutes on first run...")
    
    try:
        # Try to install browsers
        result = subprocess.run([
            sys.executable, "-m", "playwright", "install", "chromium"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Browsers installed successfully!")
            return True
        else:
            print(f"❌ Failed to install browsers: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Browser installation timed out")
        return False
    except Exception as e:
        print(f"❌ Error installing browsers: {e}")
        return False


def check_browsers():
    """Check if browsers are available"""
    try:
        # Try to import playwright and check if browsers are available
        import playwright
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            # Try to get browser executable path
            browser_path = p.chromium.executable_path
            if browser_path and Path(browser_path).exists():
                print(f"✅ Found browser at: {browser_path}")
                return True
            else:
                print("❌ Browser executable not found")
                return False
                
    except Exception as e:
        print(f"❌ Error checking browsers: {e}")
        return False


def main():
    """Main function"""
    print("🔍 Checking for Playwright browsers...")
    
    if check_browsers():
        print("✅ Browsers are ready!")
        return True
    else:
        print("📥 Browsers not found, installing...")
        return install_browsers()


if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Browser installation failed!")
        print("Please try running 'playwright install chromium' manually")
        input("Press Enter to continue...")
    else:
        print("\n✅ Browser setup complete!")
