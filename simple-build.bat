@echo off
cd /d "%~dp0"
title Oops I Stole Your Sheet - Simple Build
color 0B

echo.
echo ========================================
echo  Oops I Stole Your Sheet - Simple Build
echo  By TheMentor - Built for LoverButtMunchkin
echo ========================================
echo.

echo Current directory: %CD%
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

echo Python version:
python --version
echo.

echo Step 1: Cleaning previous build...
if exist "build" rmdir /s /q "build" 2>nul
if exist "dist" rmdir /s /q "dist" 2>nul
if exist "__pycache__" rmdir /s /q "__pycache__" 2>nul
pyinstaller --clean-cache >nul 2>&1
echo Build files cleaned.
echo.

echo Step 2: Installing dependencies...
pip install -r requirethis.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo.

echo Step 3: Installing Playwright browsers...
playwright install chromium
if errorlevel 1 (
    echo ERROR: Failed to install browsers
    pause
    exit /b 1
)
echo.

echo Step 4: Building executable...
pyinstaller --onefile --name "OopsIStoleYourSheet" --console oopsistoleyoursheet.py
if errorlevel 1 (
    echo ERROR: Failed to build executable
    pause
    exit /b 1
)
echo.

echo Step 5: Creating launcher scripts...
echo @echo off > "dist\OopsIStoleYourSheet-GUI.bat"
echo cd /d "%%~dp0" >> "dist\OopsIStoleYourSheet-GUI.bat"
echo OopsIStoleYourSheet.exe >> "dist\OopsIStoleYourSheet-GUI.bat"

echo @echo off > "dist\OopsIStoleYourSheet-Console.bat"
echo cd /d "%%~dp0" >> "dist\OopsIStoleYourSheet-Console.bat"
echo OopsIStoleYourSheet.exe --console >> "dist\OopsIStoleYourSheet-Console.bat"
echo pause >> "dist\OopsIStoleYourSheet-Console.bat"

if exist "install-browsers.bat" copy "install-browsers.bat" "dist\" >nul

echo.
echo ========================================
echo BUILD COMPLETE!
echo ========================================
echo.
echo Your files are in the 'dist' folder:
echo - OopsIStoleYourSheet.exe
echo - OopsIStoleYourSheet-GUI.bat
echo - OopsIStoleYourSheet-Console.bat
echo - install-browsers.bat
echo.
echo FIRST TIME SETUP:
echo 1. Go to the 'dist' folder
echo 2. Run install-browsers.bat (one time only)
echo 3. Then run OopsIStoleYourSheet.exe
echo.
pause
