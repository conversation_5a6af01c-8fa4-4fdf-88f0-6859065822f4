@echo off
title Oops I Stole Your Sheet - Setup
color 0B

echo.
echo ========================================
echo  Oops I Stole Your Sheet - Setup
echo  By TheMentor - Built for LoverButtMunchkin
echo ========================================
echo.

echo This script will help you set up the development environment
echo or build the executable for distribution.
echo.

:MENU
echo Please choose an option:
echo.
echo 1. Install dependencies only (for development)
echo 2. Build executable (full build process)
echo 3. Quick test run (console mode)
echo 4. Quick test run (GUI mode)
echo 5. Clean build files
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto INSTALL_DEPS
if "%choice%"=="2" goto BUILD_EXE
if "%choice%"=="3" goto TEST_CONSOLE
if "%choice%"=="4" goto TEST_GUI
if "%choice%"=="5" goto CLEAN
if "%choice%"=="6" goto EXIT
echo Invalid choice. Please try again.
goto MENU

:INSTALL_DEPS
echo.
echo Installing dependencies...
echo.
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    goto MENU
)

pip install -r requirethis.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    goto MENU
)

echo.
echo Installing Playwright browsers...
playwright install chromium
if errorlevel 1 (
    echo ERROR: Failed to install Playwright browsers
    pause
    goto MENU
)

echo.
echo Dependencies installed successfully!
pause
goto MENU

:BUILD_EXE
echo.
echo Starting full build process...
echo This may take several minutes...
echo.
call build.bat
pause
goto MENU

:TEST_CONSOLE
echo.
echo Testing console mode...
echo.
python oopsistoleyoursheet.py --console
pause
goto MENU

:TEST_GUI
echo.
echo Testing GUI mode...
echo.
python oopsistoleyoursheet.py
pause
goto MENU

:CLEAN
echo.
echo Cleaning build files...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"
if exist "*.spec" del "*.spec"
echo Build files cleaned.
pause
goto MENU

:EXIT
echo.
echo Thank you for using Oops I Stole Your Sheet!
echo.
pause
exit /b 0
