import{u as Me,r as K,e as Be,_ as Ne,f as Re,g as Pe,a as h,j as _,R as f,h as De,c as Le,s as ve,S as Fe,i as ee,T as B,t as Ie,m as We,k as Oe,l as he,W as je,M as Ae,n as ze,b as Ve,d as Ke}from"./assets/wsPort-937c7b8a.js";class fe{constructor(){this._stringCache=new Map}internString(e){let t=this._stringCache.get(e);return t||(this._stringCache.set(e,e),t=e),t}}class pe{constructor(e,t,s,i){this._tests=new Map,this._listOnly=!1,this._clearPreviousResultsWhenTestBegins=!1,this._stringPool=new fe,this._rootSuite=new j("","root"),this._pathSeparator=e,this._reporter=t,this._reuseTestCases=s,this._reportConfig=i}dispatch(e){const{method:t,params:s}=e;if(t==="onConfigure"){this._onConfigure(s.config);return}if(t==="onProject"){this._onProject(s.project);return}if(t==="onBegin"){this._onBegin();return}if(t==="onTestBegin"){this._onTestBegin(s.testId,s.result);return}if(t==="onTestEnd"){this._onTestEnd(s.test,s.result);return}if(t==="onStepBegin"){this._onStepBegin(s.testId,s.resultId,s.step);return}if(t==="onStepEnd"){this._onStepEnd(s.testId,s.resultId,s.step);return}if(t==="onError"){this._onError(s.error);return}if(t==="onStdIO"){this._onStdIO(s.type,s.testId,s.resultId,s.data,s.isBase64);return}if(t==="onEnd")return this._onEnd(s.result);if(t==="onExit")return this._onExit()}_setClearPreviousResultsWhenTestBegins(){this._clearPreviousResultsWhenTestBegins=!0}_onConfigure(e){var t,s;this._rootDir=e.rootDir,this._listOnly=e.listOnly,this._config=this._parseConfig(e),(s=(t=this._reporter).onConfigure)==null||s.call(t,this._config)}_onProject(e){let t=this._rootSuite.suites.find(i=>i.project().__projectId===e.id);t||(t=new j(e.name,"project"),this._rootSuite.suites.push(t),t.parent=this._rootSuite);const s=this._parseProject(e);if(t.project=()=>s,this._mergeSuitesInto(e.suites,t),this._listOnly){const i=new Set,r=c=>{c.tests.map(a=>a.testId).forEach(a=>i.add(a)),c.suites.forEach(r)};e.suites.forEach(r);const o=c=>{c.tests=c.tests.filter(a=>i.has(a.id)),c.suites.forEach(o)};o(t)}}_onBegin(){var e,t;(t=(e=this._reporter).onBegin)==null||t.call(e,this._rootSuite)}_onTestBegin(e,t){var r,o;const s=this._tests.get(e);this._clearPreviousResultsWhenTestBegins&&s._clearResults();const i=s._createTestResult(t.id);i.retry=t.retry,i.workerIndex=t.workerIndex,i.parallelIndex=t.parallelIndex,i.setStartTimeNumber(t.startTime),i.statusEx="running",(o=(r=this._reporter).onTestBegin)==null||o.call(r,s,i)}_onTestEnd(e,t){var r,o,c;const s=this._tests.get(e.testId);s.timeout=e.timeout,s.expectedStatus=e.expectedStatus,s.annotations=e.annotations;const i=s.resultsMap.get(t.id);i.duration=t.duration,i.status=t.status,i.statusEx=t.status,i.errors=t.errors,i.error=(r=i.errors)==null?void 0:r[0],i.attachments=this._parseAttachments(t.attachments),(c=(o=this._reporter).onTestEnd)==null||c.call(o,s,i),i.stepMap=new Map}_onStepBegin(e,t,s){var p,d;const i=this._tests.get(e),r=i.resultsMap.get(t),o=s.parentStepId?r.stepMap.get(s.parentStepId):void 0,c=this._absoluteLocation(s.location),a=new $e(s,o,c);o?o.steps.push(a):r.steps.push(a),r.stepMap.set(s.id,a),(d=(p=this._reporter).onStepBegin)==null||d.call(p,i,r,a)}_onStepEnd(e,t,s){var c,a;const i=this._tests.get(e),r=i.resultsMap.get(t),o=r.stepMap.get(s.id);o.duration=s.duration,o.error=s.error,(a=(c=this._reporter).onStepEnd)==null||a.call(c,i,r,o)}_onError(e){var t,s;(s=(t=this._reporter).onError)==null||s.call(t,e)}_onStdIO(e,t,s,i,r){var p,d,b,g;const o=r?globalThis.Buffer?Buffer.from(i,"base64"):atob(i):i,c=t?this._tests.get(t):void 0,a=c&&s?c.resultsMap.get(s):void 0;e==="stdout"?(a==null||a.stdout.push(o),(d=(p=this._reporter).onStdOut)==null||d.call(p,o,c,a)):(a==null||a.stderr.push(o),(g=(b=this._reporter).onStdErr)==null||g.call(b,o,c,a))}async _onEnd(e){var t,s;await((s=(t=this._reporter).onEnd)==null?void 0:s.call(t,{status:e.status,startTime:new Date(e.startTime),duration:e.duration}))}_onExit(){var e,t;return this._stringPool=new fe,(t=(e=this._reporter).onExit)==null?void 0:t.call(e)}_parseConfig(e){const t={...be,...e};return this._reportConfig&&(t.configFile=this._reportConfig.configFile,t.reportSlowTests=this._reportConfig.reportSlowTests,t.quiet=this._reportConfig.quiet,t.reporter=[...this._reportConfig.reporter]),t}_parseProject(e){return{__projectId:e.id,metadata:e.metadata,name:e.name,outputDir:this._absolutePath(e.outputDir),repeatEach:e.repeatEach,retries:e.retries,testDir:this._absolutePath(e.testDir),testIgnore:G(e.testIgnore),testMatch:G(e.testMatch),timeout:e.timeout,grep:G(e.grep),grepInvert:G(e.grepInvert),dependencies:e.dependencies,teardown:e.teardown,snapshotDir:this._absolutePath(e.snapshotDir),use:{}}}_parseAttachments(e){return e.map(t=>({...t,body:t.base64&&globalThis.Buffer?Buffer.from(t.base64,"base64"):void 0}))}_mergeSuitesInto(e,t){for(const s of e){let i=t.suites.find(r=>r.title===s.title);i||(i=new j(s.title,s.type),i.parent=t,t.suites.push(i)),i.location=this._absoluteLocation(s.location),i._fileId=s.fileId,i._parallelMode=s.parallelMode,this._mergeSuitesInto(s.suites,i),this._mergeTestsInto(s.tests,i)}}_mergeTestsInto(e,t){for(const s of e){let i=this._reuseTestCases?t.tests.find(r=>r.title===s.title):void 0;i||(i=new Ue(s.testId,s.title,this._absoluteLocation(s.location)),i.parent=t,t.tests.push(i),this._tests.set(i.id,i)),this._updateTest(s,i)}}_updateTest(e,t){return t.id=e.testId,t.location=this._absoluteLocation(e.location),t.retries=e.retries,t}_absoluteLocation(e){return e&&{...e,file:this._absolutePath(e.file)}}_absolutePath(e){return e&&this._stringPool.internString(this._rootDir+this._pathSeparator+e)}}class j{constructor(e,t){this._requireFile="",this.suites=[],this.tests=[],this._parallelMode="none",this.title=e,this._type=t}allTests(){const e=[],t=s=>{for(const i of[...s.suites,...s.tests])i instanceof j?t(i):e.push(i)};return t(this),e}titlePath(){const e=this.parent?this.parent.titlePath():[];return(this.title||this._type!=="describe")&&e.push(this.title),e}project(){}}class Ue{constructor(e,t,s){this.fn=()=>{},this.results=[],this.expectedStatus="passed",this.timeout=0,this.annotations=[],this.retries=0,this.repeatEachIndex=0,this.resultsMap=new Map,this.id=e,this.title=t,this.location=s}titlePath(){const e=this.parent?this.parent.titlePath():[];return e.push(this.title),e}outcome(){const e=this.results.filter(s=>s.status!=="interrupted");if(e.every(s=>s.status==="skipped"))return"skipped";const t=e.filter(s=>s.status!==this.expectedStatus);return t.length?t.length===e.length?"unexpected":"flaky":"expected"}ok(){const e=this.outcome();return e==="expected"||e==="flaky"||e==="skipped"}_clearResults(){this.results=[],this.resultsMap.clear()}_createTestResult(e){const t=new He(this.results.length);return this.results.push(t),this.resultsMap.set(e,t),t}}class $e{constructor(e,t,s){this.duration=-1,this.steps=[],this._startTime=0,this.title=e.title,this.category=e.category,this.location=s,this.parent=t,this._startTime=e.startTime}titlePath(){var t;return[...((t=this.parent)==null?void 0:t.titlePath())||[],this.title]}get startTime(){return new Date(this._startTime)}set startTime(e){this._startTime=+e}}class He{constructor(e){this.parallelIndex=-1,this.workerIndex=-1,this.duration=-1,this.stdout=[],this.stderr=[],this.attachments=[],this.status="skipped",this.steps=[],this.errors=[],this.stepMap=new Map,this.statusEx="scheduled",this._startTime=0,this.retry=e}setStartTimeNumber(e){this._startTime=e}get startTime(){return new Date(this._startTime)}set startTime(e){this._startTime=+e}}const be={forbidOnly:!1,fullyParallel:!1,globalSetup:null,globalTeardown:null,globalTimeout:0,grep:/.*/,grepInvert:null,maxFailures:0,metadata:{},preserveOutput:"always",projects:[],reporter:[[{}.CI?"dot":"list"]],reportSlowTests:{max:5,threshold:15e3},configFile:"",rootDir:"",quiet:!1,shard:null,updateSnapshots:"missing",version:"",workers:0,webServer:null};function G(n){return n.map(e=>e.s?e.s:new RegExp(e.r.source,e.r.flags))}const qe=({source:n})=>{const[e,t]=Me(),[s,i]=K.useState(Be()),[r]=K.useState(Ne(()=>import("./assets/xtermModule-443332e6.js"),["./assets/xtermModule-443332e6.js","./xtermModule.6428296b.css"],import.meta.url).then(c=>c.default)),o=K.useRef(null);return K.useEffect(()=>(Re(i),()=>Pe(i)),[]),K.useEffect(()=>{const c=n.write,a=n.clear;return(async()=>{const{Terminal:p,FitAddon:d}=await r,b=t.current;if(!b)return;const g=s==="dark-mode"?Xe:Ye;if(o.current&&o.current.terminal.options.theme===g)return;o.current&&(b.textContent="");const l=new p({convertEol:!0,fontSize:13,scrollback:1e4,fontFamily:"var(--vscode-editor-font-family)",theme:g}),m=new d;l.loadAddon(m);for(const v of n.pending)l.write(v);n.write=v=>{n.pending.push(v),l.write(v)},n.clear=()=>{n.pending=[],l.clear()},l.open(b),m.fit(),o.current={terminal:l,fitAddon:m}})(),()=>{n.clear=a,n.write=c}},[r,o,t,n,s]),K.useEffect(()=>{setTimeout(()=>{o.current&&(o.current.fitAddon.fit(),n.resize(o.current.terminal.cols,o.current.terminal.rows))},250)},[e,n]),h("div",{"data-testid":"output",className:"xterm-wrapper",style:{flex:"auto"},ref:t})},Ye={foreground:"#383a42",background:"#fafafa",cursor:"#383a42",black:"#000000",red:"#e45649",green:"#50a14f",yellow:"#c18401",blue:"#4078f2",magenta:"#a626a4",cyan:"#0184bc",white:"#a0a0a0",brightBlack:"#000000",brightRed:"#e06c75",brightGreen:"#98c379",brightYellow:"#d19a66",brightBlue:"#4078f2",brightMagenta:"#a626a4",brightCyan:"#0184bc",brightWhite:"#383a42",selectionBackground:"#d7d7d7",selectionForeground:"#383a42"},Xe={foreground:"#f8f8f2",background:"#1e1e1e",cursor:"#f8f8f0",black:"#000000",red:"#ff5555",green:"#50fa7b",yellow:"#f1fa8c",blue:"#bd93f9",magenta:"#ff79c6",cyan:"#8be9fd",white:"#bfbfbf",brightBlack:"#4d4d4d",brightRed:"#ff6e6e",brightGreen:"#69ff94",brightYellow:"#ffffa5",brightBlue:"#d6acff",brightMagenta:"#ff92df",brightCyan:"#a4ffff",brightWhite:"#e6e6e6",selectionBackground:"#44475a",selectionForeground:"#f8f8f2"};const Je=({title:n,children:e,setExpanded:t,expanded:s,expandOnTitleClick:i})=>_("div",{className:"expandable"+(s?" expanded":""),children:[_("div",{className:"expandable-title",onClick:()=>i&&t(!s),children:[h("div",{className:"codicon codicon-"+(s?"chevron-down":"chevron-right"),style:{cursor:"pointer",color:"var(--vscode-foreground)",marginLeft:"5px"},onClick:()=>!i&&t(!s)}),n]}),s&&h("div",{style:{marginLeft:25},children:e})]});function Qe(n){return`.playwright-artifacts-${n}`}let ae=()=>{},_e=n=>{},Se={cols:80,rows:24},O=async()=>{};const A={pending:[],clear:()=>{},write:n=>A.pending.push(n),resize:(n,e)=>{Se={cols:n,rows:e},Y("resizeTerminal",{cols:n,rows:e})}},Ze=({})=>{var de;const[n,e]=f.useState(""),[t,s]=f.useState(!1),[i,r]=f.useState(new Map([["passed",!1],["failed",!1],["skipped",!1]])),[o,c]=f.useState(new Map),[a,p]=f.useState({config:void 0,rootSuite:void 0,loadErrors:[]}),[d,b]=f.useState(),[g,l]=f.useState({}),[m,v]=f.useState(new Set),[E,N]=f.useState(!1),[S,D]=f.useState(),[R,J]=De("watch-all",!1),[se,z]=f.useState({value:new Set}),u=f.useRef(Promise.resolve()),k=f.useRef(new Set),[w,T]=f.useState(0),[C,y]=f.useState(!1),[Q,ue]=f.useState(!0),Ee=f.useRef(null),ie=f.useCallback(()=>{N(!0),z({value:new Set}),ae(be,new j("","root"),[],void 0),Te(!0).then(async()=>{N(!1);const{hasBrowsers:x}=await O("checkBrowsers");ue(x)})},[]);f.useEffect(()=>{var x;(x=Ee.current)==null||x.focus(),N(!0),Le({onEvent:it,onClose:()=>y(!0)}).then(M=>{O=async(L,F)=>{const P=window.__logForTest;P==null||P({method:L,params:F}),await M(L,F)},ie()})},[ie]),ae=f.useCallback((x,M,L,F)=>{const P=x.configFile?ve.getObject(x.configFile+":projects",void 0):void 0;for(const I of o.keys())M.suites.find(Z=>Z.title===I)||o.delete(I);for(const I of M.suites)o.has(I.title)||o.set(I.title,!!(P!=null&&P.includes(I.title)));!P&&o.size&&![...o.values()].includes(!0)&&o.set(o.entries().next().value[0],!0),p({config:x,rootSuite:M,loadErrors:L}),c(new Map(o)),S&&F?b(F):F||b(void 0)},[o,S]);const ne=f.useCallback((x,M)=>{x==="bounce-if-busy"&&S||(k.current=new Set([...k.current,...M]),u.current=u.current.then(async()=>{var P,I,Z;const L=k.current;if(k.current=new Set,!L.size)return;{for(const W of((P=a.rootSuite)==null?void 0:P.allTests())||[])L.has(W.id)&&(W._clearResults(),W._createTestResult("pending"));p({...a})}const F="  ["+new Date().toLocaleTimeString()+"]";A.write("\x1B[2m—".repeat(Math.max(0,Se.cols-F.length))+F+"\x1B[22m"),b({total:0,passed:0,failed:0,skipped:0}),D({testIds:L}),await O("run",{testIds:[...L],projects:[...o].filter(([W,ye])=>ye).map(([W])=>W)});for(const W of((I=a.rootSuite)==null?void 0:I.allTests())||[])((Z=W.results[0])==null?void 0:Z.duration)===-1&&W._clearResults();p({...a}),D(void 0)}))},[o,S,a]),V=!!S,oe=f.useRef(null),Ce=f.useCallback(x=>{var M;x.preventDefault(),x.stopPropagation(),(M=oe.current)==null||M.showModal()},[]),re=f.useCallback(x=>{var M;x.preventDefault(),x.stopPropagation(),(M=oe.current)==null||M.close()},[]),xe=f.useCallback(x=>{re(x),s(!0),O("installBrowsers").then(async()=>{s(!1);const{hasBrowsers:M}=await O("checkBrowsers");ue(M)})},[re]);return _("div",{className:"vbox ui-mode",children:[!Q&&_("dialog",{ref:oe,children:[_("div",{className:"title",children:[h("span",{className:"codicon codicon-lightbulb"}),"Install browsers"]}),_("div",{className:"body",children:["Playwright did not find installed browsers.",h("br",{}),"Would you like to run `playwright install`?",h("br",{}),h("button",{className:"button",onClick:xe,children:"Install"}),h("button",{className:"button secondary",onClick:re,children:"Dismiss"})]})]}),C&&_("div",{className:"drop-target",children:[h("div",{className:"title",children:"UI Mode disconnected"}),_("div",{children:[h("a",{href:"#",onClick:()=>window.location.reload(),children:"Reload the page"})," to reconnect"]})]}),_(Fe,{sidebarSize:250,minSidebarSize:150,orientation:"horizontal",sidebarIsFirst:!0,settingName:"testListSidebar",children:[_("div",{className:"vbox",children:[_("div",{className:"vbox"+(t?"":" hidden"),children:[_(ee,{children:[h("div",{className:"section-title",style:{flex:"none"},children:"Output"}),h(B,{icon:"circle-slash",title:"Clear output",onClick:()=>A.clear()}),h("div",{className:"spacer"}),h(B,{icon:"close",title:"Close",onClick:()=>s(!1)})]}),h(qe,{source:A})]}),h("div",{className:"vbox"+(t?" hidden":""),children:h(st,{item:g,rootDir:(de=a.config)==null?void 0:de.rootDir})})]}),_("div",{className:"vbox ui-mode-sidebar",children:[_(ee,{noShadow:!0,noMinHeight:!0,children:[h("img",{src:"playwright-logo.svg",alt:"Playwright logo"}),h("div",{className:"section-title",children:"Playwright"}),h(B,{icon:"color-mode",title:"Toggle color mode",onClick:()=>Ie()}),h(B,{icon:"refresh",title:"Reload",onClick:()=>ie(),disabled:V||E}),h(B,{icon:"terminal",title:"Toggle output",toggled:t,onClick:()=>{s(!t)}}),!Q&&h(B,{icon:"lightbulb-autofix",style:{color:"var(--vscode-list-warningForeground)"},title:"Playwright browsers are missing",onClick:Ce})]}),h(Ge,{filterText:n,setFilterText:e,statusFilters:i,setStatusFilters:r,projectFilters:o,setProjectFilters:c,testModel:a,runTests:()=>ne("bounce-if-busy",m)}),_(ee,{noMinHeight:!0,children:[!V&&!d&&h("div",{className:"section-title",children:"Tests"}),!V&&d&&h("div",{"data-testid":"status-line",className:"status-line",children:_("div",{children:[d.passed,"/",d.total," passed (",d.passed/d.total*100|0,"%)"]})}),V&&d&&h("div",{"data-testid":"status-line",className:"status-line",children:_("div",{children:["Running ",d.passed,"/",S.testIds.size," passed (",d.passed/S.testIds.size*100|0,"%)"]})}),h(B,{icon:"play",title:"Run all",onClick:()=>ne("bounce-if-busy",m),disabled:V||E}),h(B,{icon:"debug-stop",title:"Stop",onClick:()=>Y("stop"),disabled:!V||E}),h(B,{icon:"eye",title:"Watch all",toggled:R,onClick:()=>{z({value:new Set}),J(!R)}}),h(B,{icon:"collapse-all",title:"Collapse all",onClick:()=>{T(w+1)}})]}),h(tt,{statusFilters:i,projectFilters:o,filterText:n,testModel:a,runningState:S,runTests:ne,onItemSelected:l,setVisibleTestIds:v,watchAll:R,watchedTreeIds:se,setWatchedTreeIds:z,isLoading:E,requestedCollapseAllCount:w})]})]})]})},Ge=({filterText:n,setFilterText:e,statusFilters:t,setStatusFilters:s,projectFilters:i,setProjectFilters:r,testModel:o,runTests:c})=>{const[a,p]=f.useState(!1),d=f.useRef(null);f.useEffect(()=>{var l;(l=d.current)==null||l.focus()},[]);const b=[...t.entries()].filter(([l,m])=>m).map(([l])=>l).join(" ")||"all",g=[...i.entries()].filter(([l,m])=>m).map(([l])=>l).join(" ")||"all";return _("div",{className:"filters",children:[h(Je,{expanded:a,setExpanded:p,title:h("input",{ref:d,type:"search",placeholder:"Filter (e.g. text, @tag)",spellCheck:!1,value:n,onChange:l=>{e(l.target.value)},onKeyDown:l=>{l.key==="Enter"&&c()}})}),_("div",{className:"filter-summary",title:"Status: "+b+`
Projects: `+g,onClick:()=>p(!a),children:[h("span",{className:"filter-label",children:"Status:"})," ",b,h("span",{className:"filter-label",children:"Projects:"})," ",g]}),a&&_("div",{className:"hbox",style:{marginLeft:14},children:[h("div",{className:"filter-list",children:[...t.entries()].map(([l,m])=>h("div",{className:"filter-entry",children:_("label",{children:[h("input",{type:"checkbox",checked:m,onClick:()=>{const v=new Map(t);v.set(l,!v.get(l)),s(v)}}),h("div",{children:l})]})}))}),h("div",{className:"filter-list",children:[...i.entries()].map(([l,m])=>h("div",{className:"filter-entry",children:_("label",{children:[h("input",{type:"checkbox",checked:m,onClick:()=>{var N;const v=new Map(i);v.set(l,!v.get(l)),r(v);const E=(N=o==null?void 0:o.config)==null?void 0:N.configFile;E&&ve.setObject(E+":projects",[...v.entries()].filter(([S,D])=>D).map(([S])=>S))}}),h("div",{children:l||"untitled"})]})}))})]})]})},et=ze,tt=({statusFilters:n,projectFilters:e,filterText:t,testModel:s,runTests:i,runningState:r,watchAll:o,watchedTreeIds:c,setWatchedTreeIds:a,isLoading:p,onItemSelected:d,setVisibleTestIds:b,requestedCollapseAllCount:g})=>{const[l,m]=f.useState({expandedItems:new Map}),[v,E]=f.useState(),[N,S]=f.useState(g),{rootItem:D,treeItemMap:R,fileNames:J}=f.useMemo(()=>{let u=rt(s.rootSuite,s.loadErrors,e);lt(u,t,n,r==null?void 0:r.testIds),ke(u),u=at(u),ct(u);const k=new Map,w=new Set,T=new Set,C=y=>{y.kind==="group"&&y.location.file&&T.add(y.location.file),y.kind==="case"&&y.tests.forEach(Q=>w.add(Q.id)),y.children.forEach(C),k.set(y.id,y)};return C(u),b(w),{rootItem:u,treeItemMap:k,fileNames:T}},[t,s,n,e,b,r]);f.useEffect(()=>{if(N!==g){l.expandedItems.clear();for(const w of R.keys())l.expandedItems.set(w,!1);S(g),E(void 0),m({...l});return}if(!r||r.itemSelectedByUser)return;let u;const k=w=>{var T;w.children.forEach(k),!u&&w.status==="failed"&&(w.kind==="test"&&r.testIds.has(w.test.id)||w.kind==="case"&&r.testIds.has((T=w.tests[0])==null?void 0:T.id))&&(u=w)};k(D),u&&E(u.id)},[r,E,D,N,S,g,l,m,R]);const{selectedTreeItem:se}=f.useMemo(()=>{const u=v?R.get(v):void 0;let k;u&&(k={file:u.location.file,line:u.location.line,source:{errors:s.loadErrors.filter(T=>{var C;return((C=T.location)==null?void 0:C.file)===u.location.file}).map(T=>({line:T.location.line,message:T.message})),content:void 0}});let w;return(u==null?void 0:u.kind)==="test"?w=u.test:(u==null?void 0:u.kind)==="case"&&u.tests.length===1&&(w=u.tests[0]),d({treeItem:u,testCase:w,testFile:k}),{selectedTreeItem:u}},[d,v,s,R]);f.useEffect(()=>{if(!p)if(o)Y("watch",{fileNames:[...J]});else{const u=new Set;for(const k of c.value){const w=R.get(k),T=w==null?void 0:w.location.file;T&&u.add(T)}Y("watch",{fileNames:[...u]})}},[p,D,J,o,c,R]);const z=u=>{E(u.id),i("bounce-if-busy",le(u))};return _e=u=>{const k=[],w=new Set(u);if(o){const T=C=>{const y=C.location.file;y&&w.has(y)&&k.push(...le(C)),C.kind==="group"&&C.subKind==="folder"&&C.children.forEach(T)};T(D)}else for(const T of c.value){const C=R.get(T),y=C==null?void 0:C.location.file;y&&w.has(y)&&k.push(...le(C))}i("queue-if-busy",new Set(k))},h(et,{name:"tests",treeState:l,setTreeState:m,rootItem:D,dataTestId:"test-tree",render:u=>_("div",{className:"hbox ui-mode-list-item",children:[h("div",{className:"ui-mode-list-item-title",title:u.title,children:u.title}),!!u.duration&&u.status!=="skipped"&&h("div",{className:"ui-mode-list-item-time",children:We(u.duration)}),_(ee,{noMinHeight:!0,noShadow:!0,children:[h(B,{icon:"play",title:"Run",onClick:()=>z(u),disabled:!!r}),h(B,{icon:"go-to-file",title:"Open in VS Code",onClick:()=>Y("open",{location:ot(u)}),style:u.kind==="group"&&u.subKind==="folder"?{visibility:"hidden"}:{}}),!o&&h(B,{icon:"eye",title:"Watch",onClick:()=>{c.value.has(u.id)?c.value.delete(u.id):c.value.add(u.id),a({...c})},toggled:c.value.has(u.id)})]})]}),icon:u=>Oe(u.status),selectedItem:se,onAccepted:z,onSelected:u=>{r&&(r.itemSelectedByUser=!0),E(u.id)},isError:u=>u.kind==="group"?u.hasLoadErrors:!1,autoExpandDepth:t?5:1,noItemsMessage:p?"Loading…":"No tests"})},st=({item:n,rootDir:e})=>{var g;const[t,s]=f.useState(),[i,r]=f.useState(0),o=f.useRef(null),{outputDir:c}=f.useMemo(()=>({outputDir:n.testCase?nt(n.testCase):void 0}),[n]),[a,p]=f.useState(),d=f.useCallback(l=>p(he(l)),[p]),b=a?t==null?void 0:t.model.actions.find(l=>he(l)===a):void 0;return f.useEffect(()=>{var E,N;o.current&&clearTimeout(o.current);const l=(E=n.testCase)==null?void 0:E.results[0];if(!l){s(void 0);return}const m=l&&l.duration>=0&&l.attachments.find(S=>S.name==="trace");if(m&&m.path){we(m.path).then(S=>s({model:S,isLive:!1}));return}if(!c){s(void 0);return}const v=`${c}/${Qe(l.workerIndex)}/traces/${(N=n.testCase)==null?void 0:N.id}.json`;return o.current=setTimeout(async()=>{try{const S=await we(v);s({model:S,isLive:!0})}catch{s(void 0)}finally{r(i+1)}},500),()=>{o.current&&clearTimeout(o.current)}},[c,n,s,i,r]),h(je,{model:t==null?void 0:t.model,hideStackFrames:!0,showSourcesFirst:!0,rootDir:e,initialSelection:b,onSelectionChanged:d,fallbackLocation:n.testFile,isLive:t==null?void 0:t.isLive,status:(g=n.treeItem)==null?void 0:g.status},"workbench")};let H,q,ge,te,$;const me=()=>{clearTimeout(te),te=void 0,ae($.config,$.rootSuite,$.loadErrors,$.progress)},U=(n,e,t,s,i=!1)=>{$={config:n,rootSuite:e,loadErrors:t,progress:s},i?me():te||(te=setTimeout(me,250))},Te=n=>{if(!n)return O("list",{});let e;const t=[],s={total:0,passed:0,failed:0,skipped:0};let i;return H=new pe(X,{version:()=>"v2",onConfigure:r=>{i=r,q=new pe(X,{onBegin:o=>{ge=o.allTests().length,q=void 0}},!1)},onBegin:r=>{e||(e=r),s.total=ge,s.passed=0,s.failed=0,s.skipped=0,U(i,e,t,s,!0)},onEnd:()=>{U(i,e,t,s,!0)},onTestBegin:()=>{U(i,e,t,s)},onTestEnd:r=>{r.outcome()==="skipped"?++s.skipped:r.outcome()==="unexpected"?++s.failed:++s.passed,U(i,e,t,s)},onError:r=>{A.write((r.stack||r.value||"")+`
`),t.push(r),U(i,e??new j("","root"),t,s)},printsToStdio:()=>!1,onStdOut:()=>{},onStdErr:()=>{},onExit:()=>{},onStepBegin:()=>{},onStepEnd:()=>{}},!0),H._setClearPreviousResultsWhenTestBegins(),O("list",{})},Y=(n,e)=>{if(window._overrideProtocolForTest){window._overrideProtocolForTest({method:n,params:e}).catch(()=>{});return}O(n,e).catch(t=>{console.error(t)})},it=(n,e)=>{var t,s;if(n==="listChanged"){Te(!1).catch(()=>{});return}if(n==="testFilesChanged"){_e(e.testFileNames);return}if(n==="stdio"){if(e.buffer){const i=atob(e.buffer);A.write(i)}else A.write(e.text);return}(t=q==null?void 0:q.dispatch({method:n,params:e}))==null||t.catch(()=>{}),(s=H==null?void 0:H.dispatch({method:n,params:e}))==null||s.catch(()=>{})},nt=n=>{var e;for(let t=n.parent;t;t=t.parent)if(t.project())return(e=t.project())==null?void 0:e.outputDir},ot=n=>{if(n)return n.location.file+":"+n.location.line},le=n=>{const e=new Set;if(!n)return e;const t=s=>{var i;s.kind==="case"?s.tests.map(r=>r.id).forEach(r=>e.add(r)):s.kind==="test"?e.add(s.id):(i=s.children)==null||i.forEach(t)};return t(n),e};function ce(n,e,t,s){if(e.length===0)return n;const i=e.join(X),r=s.get(i);if(r)return r;const o=ce(n,e.slice(0,e.length-1),!1,s),c={kind:"group",subKind:t?"file":"folder",id:i,title:e[e.length-1],location:{file:i,line:0,column:0},duration:0,parent:o,children:[],status:"none",hasLoadErrors:!1};return o.children.push(c),s.set(i,c),c}function rt(n,e,t){const s=[...t.values()].some(Boolean),i={kind:"group",subKind:"folder",id:"root",title:"",location:{file:"",line:0,column:0},duration:0,parent:void 0,children:[],status:"none",hasLoadErrors:!1},r=(c,a,p)=>{for(const d of a.suites){const b=d.title||"<anonymous>";let g=p.children.find(l=>l.kind==="group"&&l.title===b);g||(g={kind:"group",subKind:"describe",id:"suite:"+a.titlePath().join("")+""+b,title:b,location:d.location,duration:0,parent:p,children:[],status:"none",hasLoadErrors:!1},p.children.push(g)),r(c,d,g)}for(const d of a.tests){const b=d.title;let g=p.children.find(v=>v.kind!=="group"&&v.title===b);g||(g={kind:"case",id:"test:"+d.titlePath().join(""),title:b,parent:p,children:[],tests:[],location:d.location,duration:0,status:"none"},p.children.push(g));const l=d.results[0];let m="none";(l==null?void 0:l.statusEx)==="scheduled"?m="scheduled":(l==null?void 0:l.statusEx)==="running"?m="running":(l==null?void 0:l.status)==="skipped"?m="skipped":(l==null?void 0:l.status)==="interrupted"?m="none":l&&d.outcome()!=="expected"?m="failed":l&&d.outcome()==="expected"&&(m="passed"),g.tests.push(d),g.children.push({kind:"test",id:d.id,title:c,location:d.location,test:d,parent:g,children:[],status:m,duration:d.results.length?Math.max(0,d.results[0].duration):0,project:c}),g.duration=g.children.reduce((v,E)=>v+E.duration,0)}},o=new Map;for(const c of(n==null?void 0:n.suites)||[])if(!(s&&!t.get(c.title)))for(const a of c.suites){const p=ce(i,a.location.file.split(X),!0,o);r(c.title,a,p)}for(const c of e){if(!c.location)continue;const a=ce(i,c.location.file.split(X),!0,o);a.hasLoadErrors=!0}return i}function lt(n,e,t,s){const i=e.trim().toLowerCase().split(" "),r=[...t.values()].some(Boolean),o=a=>{const p=a.tests[0].titlePath().join(" ").toLowerCase();return!i.every(d=>p.includes(d))&&!a.tests.some(d=>s==null?void 0:s.has(d.id))?!1:(a.children=a.children.filter(d=>!r||(s==null?void 0:s.has(d.test.id))||t.get(d.status)),a.tests=a.children.map(d=>d.test),!!a.children.length)},c=a=>{const p=[];for(const d of a.children)d.kind==="case"?o(d)&&p.push(d):(c(d),(d.children.length||d.hasLoadErrors)&&p.push(d));a.children=p};c(n)}function ke(n){for(const o of n.children)ke(o);n.kind==="group"&&n.children.sort((o,c)=>o.location.file.localeCompare(c.location.file)||o.location.line-c.location.line);let e=n.children.length>0,t=n.children.length>0,s=!1,i=!1,r=!1;for(const o of n.children)t=t&&o.status==="skipped",e=e&&(o.status==="passed"||o.status==="skipped"),s=s||o.status==="failed",i=i||o.status==="running",r=r||o.status==="scheduled";i?n.status="running":r?n.status="scheduled":s?n.status="failed":t?n.status="skipped":e&&(n.status="passed")}function at(n){let e=n;for(;e.children.length===1&&e.children[0].kind==="group"&&e.children[0].subKind==="folder";)e=e.children[0];return e.location=n.location,e}function ct(n){const e=t=>{t.kind==="case"&&t.children.length===1?t.children=[]:t.children.forEach(e)};e(n)}async function we(n){const e=new URLSearchParams;e.set("trace",n);const s=await(await fetch(`contexts?${e.toString()}`)).json();return new Ae(s)}const X=navigator.userAgent.toLowerCase().includes("windows")?"\\":"/";(async()=>{if(Ve(),window.location.protocol!=="file:"){if(window.location.href.includes("isUnderTest=true")&&await new Promise(n=>setTimeout(n,1e3)),!navigator.serviceWorker)throw new Error(`Service workers are not supported.
Make sure to serve the website (${window.location}) via HTTPS or localhost.`);navigator.serviceWorker.register("sw.bundle.js"),navigator.serviceWorker.controller||await new Promise(n=>{navigator.serviceWorker.oncontrollerchange=()=>n()}),setInterval(function(){fetch("ping")},1e4)}Ke.render(h(Ze,{}),document.querySelector("#root"))})();
