================================================================================
                    OOPS I STOLE YOUR SHEET - USER GUIDE
                    By TheMentor - Built for LoverButtMunchkin
                              Version 1.0
================================================================================

QUICK START
-----------

1. RUNNING THE APPLICATION:
   - For GUI (recommended): Double-click "OopsIStoleYourSheet.exe"
   - For Console: Double-click "OopsIStoleYourSheet-Console.bat"

2. BASIC USAGE:
   - Enter the URL of the sheet music page
   - Enter a name for your PDF file (without .pdf)
   - Click "Extract Sheet Music" or press Enter
   - Wait for the process to complete
   - Your PDF will be saved in the same folder

DETAILED INSTRUCTIONS
--------------------

GUI MODE (Graphical Interface):
1. Launch OopsIStoleYourSheet.exe
2. In the "Sheet Music URL" field, paste the web address
3. In the "Output PDF Name" field, type your desired filename
4. Click "Browse" if you want to save to a specific location
5. Click "Extract Sheet Music" to start
6. Watch the progress bar and log for updates
7. A success message will appear when complete

CONSOLE MODE (Text Interface):
1. Launch OopsIStoleYourSheet-Console.bat
2. Follow the prompts to enter URL and filename
3. The program will validate your inputs
4. Watch the colorful progress indicators
5. Press Enter when prompted to exit

SUPPORTED WEBSITES
-----------------
- MuseScore (musescore.com)
- IMSLP (imslp.org)
- Other sites that display sheet music as images

TIPS FOR BEST RESULTS
--------------------
- Use the full URL including https://
- Make sure the sheet music is publicly accessible
- Choose descriptive filenames (no special characters)
- Be patient - large scores take time to process
- Check your internet connection if downloads are slow

TROUBLESHOOTING
--------------

PROBLEM: "Please enter a URL" error
SOLUTION: Make sure you've entered a complete web address starting with http:// or https://

PROBLEM: "Authentication required" message
SOLUTION: The website requires login. Try accessing the page in your browser first.

PROBLEM: "No score images found"
SOLUTION: The website might not be supported, or the URL might be incorrect.

PROBLEM: Application won't start
SOLUTION: 
- Make sure you're on Windows
- Try running as administrator
- Check if antivirus is blocking the file

PROBLEM: Slow performance
SOLUTION: This is normal for large sheet music files. The progress bar shows current status.

PROBLEM: Invalid characters in filename
SOLUTION: Don't use these characters in filenames: < > : " / \ | ? *

FILE LOCATIONS
-------------
- Your PDF files are saved in the same folder as the executable
- Temporary files are automatically cleaned up
- No personal data is stored or transmitted

COMMAND LINE OPTIONS
-------------------
- Run normally: OopsIStoleYourSheet.exe (starts GUI)
- Console mode: OopsIStoleYourSheet.exe --console
- Alternative: OopsIStoleYourSheet.exe -c
- Help: OopsIStoleYourSheet.exe --help

WHAT THE APPLICATION DOES
-------------------------
1. Opens the webpage you specify
2. Scrolls through the page to find all sheet music images
3. Downloads each image
4. Converts images to high-quality format
5. Combines all images into a single PDF file
6. Saves the PDF with your chosen name

PRIVACY & SECURITY
-----------------
- No data is collected or sent anywhere
- All processing happens on your computer
- The application only accesses the websites you specify
- No personal information is stored

SYSTEM REQUIREMENTS
------------------
- Windows 7 or later
- Internet connection
- At least 100MB free disk space
- No additional software required

GETTING HELP
-----------
If you continue to have problems:
1. Check that the website works in your regular browser
2. Try a different sheet music URL to test
3. Make sure your internet connection is stable
4. Contact the developer if issues persist

================================================================================
                        Happy sheet music collecting!
================================================================================
