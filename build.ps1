# PowerShell Build Script for Oops I Stole Your Sheet
# By TheMentor - Built for LoverButtMunchkin

# Change to script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptDir
Write-Host "Working directory: $scriptDir" -ForegroundColor Blue

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Oops I Stole Your Sheet - Build Script" -ForegroundColor Cyan
Write-Host "By TheMentor - Built for LoverButtMunchkin" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check if Python is installed
if (-not (Test-Command "python")) {
    Write-Host "ERROR: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8 or later from https://python.org" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Python version:" -ForegroundColor Green
python --version

Write-Host ""
Write-Host "Step 0: Cleaning previous build..." -ForegroundColor Yellow
# Clean previous build files
if (Test-Path "build") {
    Write-Host "Removing build directory..." -ForegroundColor Blue
    Remove-Item -Path "build" -Recurse -Force -ErrorAction SilentlyContinue
}
if (Test-Path "dist") {
    Write-Host "Removing dist directory..." -ForegroundColor Blue
    Remove-Item -Path "dist" -Recurse -Force -ErrorAction SilentlyContinue
}
if (Test-Path "__pycache__") {
    Remove-Item -Path "__pycache__" -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "Step 1: Installing dependencies..." -ForegroundColor Yellow
try {
    pip install -r requirethis.txt
    if ($LASTEXITCODE -ne 0) { throw "pip install failed" }
    Write-Host "Dependencies installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 2: Installing Playwright browsers..." -ForegroundColor Yellow
try {
    playwright install chromium
    if ($LASTEXITCODE -ne 0) { throw "playwright install failed" }
    Write-Host "Playwright browsers installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to install Playwright browsers" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 3: Building executable with PyInstaller..." -ForegroundColor Yellow
try {
    pyinstaller --clean oopsistoleyoursheet.spec
    if ($LASTEXITCODE -ne 0) { throw "pyinstaller failed" }
    Write-Host "Executable built successfully!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to build executable" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 4: Setting up Playwright browsers for distribution..." -ForegroundColor Yellow

# Create internal directory if it doesn't exist
$internalDir = "dist\OopsIStoleYourSheet\_internal"
if (-not (Test-Path $internalDir)) {
    New-Item -ItemType Directory -Path $internalDir -Force | Out-Null
}

# Try to find and copy Playwright driver
try {
    $playwrightDriver = python -c "import playwright; print(playwright.__file__.replace('__init__.py', 'driver'))" 2>$null
    if (Test-Path $playwrightDriver) {
        Write-Host "Copying Playwright driver..." -ForegroundColor Blue
        $driverDest = "$internalDir\playwright\driver"
        if (-not (Test-Path $driverDest)) {
            New-Item -ItemType Directory -Path $driverDest -Force | Out-Null
        }
        Copy-Item -Path "$playwrightDriver\*" -Destination $driverDest -Recurse -Force
        Write-Host "Playwright driver copied successfully!" -ForegroundColor Green
    }
} catch {
    Write-Host "Warning: Could not copy Playwright driver" -ForegroundColor Yellow
}

# Try to copy browsers from user directory
$playwrightBrowsers = "$env:USERPROFILE\AppData\Local\ms-playwright"
if (Test-Path $playwrightBrowsers) {
    Write-Host "Copying Playwright browsers..." -ForegroundColor Blue
    $browserDest = "$internalDir\ms-playwright"
    if (-not (Test-Path $browserDest)) {
        New-Item -ItemType Directory -Path $browserDest -Force | Out-Null
    }
    Copy-Item -Path "$playwrightBrowsers\*" -Destination $browserDest -Recurse -Force
    Write-Host "Playwright browsers copied successfully!" -ForegroundColor Green

    # Also set up a browsers.json file to help Playwright find the browsers
    $browsersJson = @{
        "chromium" = @{
            "download_path" = "ms-playwright"
        }
    } | ConvertTo-Json -Depth 3

    $browsersJson | Out-File -FilePath "$internalDir\browsers.json" -Encoding UTF8
    Write-Host "Created browsers configuration file" -ForegroundColor Green
} else {
    Write-Host "Warning: Could not find Playwright browsers directory" -ForegroundColor Yellow
    Write-Host "You may need to run 'playwright install chromium' after building" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Step 5: Creating launcher scripts..." -ForegroundColor Yellow

# Create GUI launcher
$guiLauncher = @"
@echo off
cd /d "%~dp0"
OopsIStoleYourSheet.exe
"@
$guiLauncher | Out-File -FilePath "dist\OopsIStoleYourSheet-GUI.bat" -Encoding ASCII

# Create Console launcher
$consoleLauncher = @"
@echo off
cd /d "%~dp0"
OopsIStoleYourSheet.exe --console
pause
"@
$consoleLauncher | Out-File -FilePath "dist\OopsIStoleYourSheet-Console.bat" -Encoding ASCII

# Copy browser installer
if (Test-Path "install-browsers.bat") {
    Copy-Item "install-browsers.bat" "dist\" -Force
    Write-Host "Browser installer copied to dist folder" -ForegroundColor Green
}

Write-Host "Launcher scripts created successfully!" -ForegroundColor Green

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "BUILD COMPLETE!" -ForegroundColor Green -BackgroundColor Black
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Your executable is ready in the 'dist' folder:" -ForegroundColor White
Write-Host "- OopsIStoleYourSheet.exe (main executable)" -ForegroundColor Yellow
Write-Host "- OopsIStoleYourSheet-GUI.bat (GUI launcher)" -ForegroundColor Yellow
Write-Host "- OopsIStoleYourSheet-Console.bat (Console launcher)" -ForegroundColor Yellow
Write-Host "- install-browsers.bat (browser installer)" -ForegroundColor Yellow
Write-Host ""
Write-Host "To run:" -ForegroundColor White
Write-Host "1. First time: Double-click install-browsers.bat to install browsers" -ForegroundColor Cyan
Write-Host "2. Then: Double-click OopsIStoleYourSheet.exe for GUI mode" -ForegroundColor Cyan
Write-Host "3. Or: Double-click OopsIStoleYourSheet-Console.bat for console mode" -ForegroundColor Cyan
Write-Host "4. Command line: OopsIStoleYourSheet.exe --console" -ForegroundColor Cyan
Write-Host ""
Write-Host "The executable includes all dependencies and should run" -ForegroundColor Green
Write-Host "on any Windows machine without requiring Python installation." -ForegroundColor Green
Write-Host ""

Read-Host "Press Enter to exit"
