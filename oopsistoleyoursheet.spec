# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

# Get the current working directory (where the spec file is located)
spec_dir = Path.cwd()

# Add the current directory to Python path
sys.path.insert(0, str(spec_dir))

block_cipher = None

# Define the main script
main_script = 'oopsistoleyoursheet.py'

# Data files to include
datas = []

# Try to find and include Playwright browsers
try:
    import playwright
    from pathlib import Path

    # Get Playwright installation directory
    playwright_dir = Path(playwright.__file__).parent
    driver_dir = playwright_dir / "driver"

    # Add Playwright driver
    if driver_dir.exists():
        datas.append((str(driver_dir), "playwright/driver"))
        print(f"Found Playwright driver at: {driver_dir}")

    # Try to find browsers in common locations
    browser_locations = [
        Path.home() / "AppData" / "Local" / "ms-playwright",
        Path(os.environ.get("PLAYWRIGHT_BROWSERS_PATH", "")) if os.environ.get("PLAYWRIGHT_BROWSERS_PATH") else None,
    ]

    for browser_path in browser_locations:
        if browser_path and browser_path.exists():
            datas.append((str(browser_path), "ms-playwright"))
            print(f"Found Playwright browsers at: {browser_path}")
            break
    else:
        print("Warning: Playwright browsers not found in standard locations")

except ImportError:
    print("Warning: Playwright not found during build")

# Hidden imports for dependencies that might not be detected automatically
hiddenimports = [
    'playwright',
    'playwright.async_api',
    'playwright._impl',
    'playwright._impl._api_structures',
    'playwright._impl._browser',
    'playwright._impl._browser_context',
    'playwright._impl._page',
    'playwright._impl._element_handle',
    'playwright._impl._frame',
    'playwright._impl._network',
    'playwright._impl._download',
    'playwright._impl._file_chooser',
    'playwright._impl._input',
    'playwright._impl._js_handle',
    'playwright._impl._keyboard',
    'playwright._impl._mouse',
    'playwright._impl._selectors',
    'playwright._impl._video',
    'playwright._impl._wait_helper',
    'playwright._impl._connection',
    'playwright._impl._transport',
    'playwright._impl._helper',
    'playwright._impl._str_utils',
    'playwright._impl._errors',
    'bs4',
    'bs4.builder',
    'bs4.builder._html5lib',
    'bs4.builder._htmlparser',
    'bs4.builder._lxml',
    'reportlab',
    'reportlab.pdfgen',
    'reportlab.pdfgen.canvas',
    'reportlab.lib',
    'reportlab.lib.pagesizes',
    'PIL',
    'PIL.Image',
    'colorama',
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'asyncio',
    'threading',
    'pathlib',
    'json',
    'urllib',
    'urllib.parse',
    'urllib.request',
    'ssl',
    'certifi',
    'charset_normalizer',
    'idna',
    'requests',
    'requests.adapters',
    'requests.auth',
    'requests.cookies',
    'requests.exceptions',
    'requests.models',
    'requests.sessions',
    'requests.structures',
    'requests.utils',
]

# Binaries to include (Playwright browsers will be handled separately)
binaries = []

a = Analysis(
    [main_script],
    pathex=[str(spec_dir)],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'jupyter',
        'notebook',
        'IPython',
        'pytest',
        'setuptools',
        'distutils',
        'wheel',
        'pip',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='OopsIStoleYourSheet',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Set to True to show console window, False to hide it
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # You can add an icon file here if you have one
    version_file=None,  # You can add version info here
)
