# Oops I Stole Your Sheet - By TheMentor - Built for <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

## I forced a Clanker to write most of the documentation since I can't be bothered. So take it with a grain of salt.

Will prob fix the butt ugly interface one day. works so yea. Will open broswer window, let it do its thang. If it breaks, open Github issue and I will get around to it..... promise. Only thing you need is the dist folder.

## Technical Details

### Dependencies
- **Playwright**: Web automation and browser control
- **BeautifulSoup4**: HTML parsing and content extraction
- **ReportLab**: PDF generation
- **Pillow**: Image processing
- **Colorama**: Console color output
- **Tkinter**: GUI framework (built into Python)

### Architecture
- Asynchronous web scraping for better performance
- Threaded GUI to prevent interface freezing
- Modular design with separate GUI and console interfaces
- Comprehensive error handling and logging

### Security
- No data collection or transmission
- All processing happens locally
- Respects website robots.txt and rate limiting
- Uses standard web browsing techniques

## License

This software is provided as-is for educational and personal use.

## Credits

**Developed by**: TheMentor  
**Built for**: <PERSON><PERSON><PERSON><PERSON><PERSON>kin  
**Version**: 1.0

---


