# Oops I Stole Your Sheet - By TheMentor - Built for LoverButt<PERSON><PERSON>kin

A powerful Windows application for extracting sheet music from websites and converting them to PDF format.

## Features

- **Dual Interface**: Choose between a sleek GUI or colorful console interface
- **Smart Extraction**: Automatically detects and extracts sheet music images from web pages
- **PDF Generation**: Converts extracted images to high-quality PDF files
- **Progress Tracking**: Real-time progress updates and detailed logging
- **Input Validation**: Ensures URLs and file names are valid before processing
- **Standalone Executable**: No Python installation required on target machines

## Quick Start

### For End Users (Pre-built Executable)

1. Download the `dist` folder containing the executable
2. Choose your preferred way to run:
   - **GUI Mode**: Double-click `OopsIStoleYourSheet.exe`
   - **Console Mode**: Double-click `OopsIStoleYourSheet-Console.bat`
   - **Command Line**: Run `OopsIStoleYourSheet.exe --console` for console mode

### For Developers (Building from Source)

#### Prerequisites
- Python 3.11 (recommended) or 3.8-3.12
- Windows operating system
- Note: Python 3.11 is recommended for best compatibility and no compilation requirements

#### Building the Executable

1. **<PERSON>lone or download the source code**
2. **Run the build script**:
   - **Option A**: Double-click `build.bat`
   - **Option B**: Run `build.ps1` in PowerShell
   - **Option C**: Manual build (see below)

#### Manual Build Process

```bash
# Install dependencies
pip install -r requirethis.txt
pip install -r build-requirements.txt

# Install Playwright browsers
playwright install chromium

# Build executable
pyinstaller --clean oopsistoleyoursheet.spec
```

## Usage

### GUI Mode (Default)

1. Launch the application (it starts in GUI mode by default)
2. Enter the URL of the sheet music page
3. Specify the output PDF name (without .pdf extension)
4. Click "Extract Sheet Music"
5. Monitor progress in the log area
6. The PDF will be saved in the same directory as the executable

### Console Mode

1. Run with `--console`, `-c`, or `--cli` flag
2. Follow the prompts to enter URL and output name
3. The application will validate inputs and guide you through the process
4. Monitor progress with colorful console output

### Supported Websites

The application works with websites that display sheet music as SVG images, particularly:
- MuseScore
- IMSLP
- Other sites with similar sheet music display formats

## Features in Detail

### Smart Web Scraping
- Automatically scrolls through pages to find all sheet music images
- Handles dynamic content loading
- Detects authentication barriers
- Anti-detection measures for better compatibility

### Image Processing
- Converts SVG images to high-quality PNG format
- Optimizes images for PDF inclusion
- Maintains aspect ratios and quality

### PDF Generation
- Creates professional-quality PDF files
- Properly scales images to fit A4 pages
- Maintains page order from the original source

### User Experience
- **GUI**: Modern dark theme with progress bars and real-time logging
- **Console**: Colorful output with emoji indicators and progress updates
- Input validation prevents common errors
- Detailed error messages and troubleshooting guidance

## Troubleshooting

### Common Issues

**"Authentication required" error**
- The website requires login to access the sheet music
- Try logging in through a regular browser first, then use the application

**"No score images found"**
- The website might not be supported
- Check if the URL is correct and accessible
- Some sites may have changed their structure

**Executable won't start**
- Ensure you're running on Windows
- Try running as administrator
- Check Windows Defender/antivirus settings

**Slow performance**
- Large sheet music files take time to process
- Network speed affects download times
- The application shows progress to keep you informed

### Getting Help

If you encounter issues:
1. Check the log output for detailed error messages
2. Ensure the URL is accessible in a regular browser
3. Try different sheet music URLs to isolate the problem
4. For persistent issues, contact the developer

## Technical Details

### Dependencies
- **Playwright**: Web automation and browser control
- **BeautifulSoup4**: HTML parsing and content extraction
- **ReportLab**: PDF generation
- **Pillow**: Image processing
- **Colorama**: Console color output
- **Tkinter**: GUI framework (built into Python)

### Architecture
- Asynchronous web scraping for better performance
- Threaded GUI to prevent interface freezing
- Modular design with separate GUI and console interfaces
- Comprehensive error handling and logging

### Security
- No data collection or transmission
- All processing happens locally
- Respects website robots.txt and rate limiting
- Uses standard web browsing techniques

## License

This software is provided as-is for educational and personal use.

## Credits

**Developed by**: TheMentor  
**Built for**: LoverButtMunchkin  
**Version**: 1.0

---

*Oops I Stole Your Sheet - Making sheet music accessible, one PDF at a time!*
