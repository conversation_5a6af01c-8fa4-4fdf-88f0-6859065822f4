@echo off
echo ========================================
echo Oops I Stole Your Sheet - Build Script
echo By TheMentor - Built for LoverButtMunchkin
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or later from https://python.org
    pause
    exit /b 1
)

echo Step 1: Installing dependencies...
pip install -r requirethis.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Step 2: Installing Playwright browsers...
playwright install chromium
if errorlevel 1 (
    echo ERROR: Failed to install Playwright browsers
    pause
    exit /b 1
)

echo.
echo Step 3: Building executable with PyInstaller...
pyinstaller --clean oopsistoleyoursheet.spec
if errorlevel 1 (
    echo ERROR: Failed to build executable
    pause
    exit /b 1
)

echo.
echo Step 4: Copying Playwright browsers to dist folder...
if not exist "dist\OopsIStoleYourSheet\_internal" mkdir "dist\OopsIStoleYourSheet\_internal"

REM Find Playwright browsers directory
for /f "tokens=*" %%i in ('python -c "import playwright; print(playwright.__file__.replace('__init__.py', 'driver'))"') do set PLAYWRIGHT_DRIVER=%%i

if exist "%PLAYWRIGHT_DRIVER%" (
    echo Copying Playwright driver from %PLAYWRIGHT_DRIVER%...
    xcopy "%PLAYWRIGHT_DRIVER%" "dist\OopsIStoleYourSheet\_internal\playwright\driver\" /E /I /Y
) else (
    echo Warning: Could not find Playwright driver directory
)

REM Also copy browsers if they exist in the user directory
set PLAYWRIGHT_BROWSERS=%USERPROFILE%\AppData\Local\ms-playwright
if exist "%PLAYWRIGHT_BROWSERS%" (
    echo Copying Playwright browsers from %PLAYWRIGHT_BROWSERS%...
    if not exist "dist\OopsIStoleYourSheet\_internal\ms-playwright" mkdir "dist\OopsIStoleYourSheet\_internal\ms-playwright"
    xcopy "%PLAYWRIGHT_BROWSERS%" "dist\OopsIStoleYourSheet\_internal\ms-playwright\" /E /I /Y
)

echo.
echo Step 5: Creating launcher scripts...

REM Create GUI launcher
echo @echo off > "dist\OopsIStoleYourSheet-GUI.bat"
echo cd /d "%%~dp0" >> "dist\OopsIStoleYourSheet-GUI.bat"
echo OopsIStoleYourSheet.exe >> "dist\OopsIStoleYourSheet-GUI.bat"

REM Create Console launcher
echo @echo off > "dist\OopsIStoleYourSheet-Console.bat"
echo cd /d "%%~dp0" >> "dist\OopsIStoleYourSheet-Console.bat"
echo OopsIStoleYourSheet.exe --console >> "dist\OopsIStoleYourSheet-Console.bat"
echo pause >> "dist\OopsIStoleYourSheet-Console.bat"

echo.
echo ========================================
echo BUILD COMPLETE!
echo ========================================
echo.
echo Your executable is ready in the 'dist' folder:
echo - OopsIStoleYourSheet.exe (main executable)
echo - OopsIStoleYourSheet-GUI.bat (GUI launcher)
echo - OopsIStoleYourSheet-Console.bat (Console launcher)
echo.
echo To run:
echo 1. Double-click OopsIStoleYourSheet.exe for GUI mode
echo 2. Double-click OopsIStoleYourSheet-Console.bat for console mode
echo 3. Or run from command line with --console flag for console mode
echo.
echo The executable includes all dependencies and should run
echo on any Windows machine without requiring Python installation.
echo.
pause
