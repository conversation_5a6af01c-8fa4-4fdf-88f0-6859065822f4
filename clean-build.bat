@echo off
title Clean Build Files
echo Cleaning build files...

REM Force remove build directories
if exist "build" (
    echo Removing build directory...
    rmdir /s /q "build" 2>nul
    if exist "build" (
        echo Trying to force remove build directory...
        takeown /f "build" /r /d y >nul 2>&1
        icacls "build" /grant administrators:F /t >nul 2>&1
        rmdir /s /q "build" 2>nul
    )
)

if exist "dist" (
    echo Removing dist directory...
    rmdir /s /q "dist" 2>nul
    if exist "dist" (
        echo Trying to force remove dist directory...
        takeown /f "dist" /r /d y >nul 2>&1
        icacls "dist" /grant administrators:F /t >nul 2>&1
        rmdir /s /q "dist" 2>nul
    )
)

if exist "__pycache__" (
    echo Removing __pycache__ directory...
    rmdir /s /q "__pycache__" 2>nul
)

REM Clean PyInstaller cache
echo Cleaning PyInstaller cache...
pyinstaller --clean-cache >nul 2>&1

echo.
echo Build files cleaned!
echo You can now run build.bat or build.ps1
echo.
pause
