import asyncio
import re
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from PIL import Image
import io
import os
from pathlib import Path


class FixedPlaywrightExtractor:
    def __init__(self):
        self.temp_dir = "temp_images"

    async def setup_browser_context(self, p):
        """Setup browser with anti-detection measures"""
        browser = await p.chromium.launch(
            headless=False,  # Keep visible for debugging
            args=[
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--no-sandbox",
                "--disable-dev-shm-usage",
            ],
        )

        context = await browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        )

        # Remove automation indicators
        await context.add_init_script(
            """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """
        )

        return browser, context

    async def smart_auth_check(self, page):
        """Smarter authentication detection - only flag actual auth barriers"""
        try:
            # Check for actual authentication barriers, not just login links
            title = await page.title()

            # These indicate actual authentication requirements
            auth_barriers = [
                "sign in to continue",
                "login required",
                "access denied",
                "unauthorized access",
                "authentication required",
                "please log in",
                "you must be logged in",
            ]

            title_lower = title.lower()

            for barrier in auth_barriers:
                if barrier in title_lower:
                    print(f"🚨 Authentication barrier detected: '{barrier}' in title")
                    return False

            # Check if we can see actual score content
            has_score_content = await page.evaluate(
                """
                () => {
                    // Look for score-related content
                    const scoreImages = document.querySelectorAll('img[src*="score_"]');
                    const musicNotation = document.querySelectorAll('.score, [class*="score"], [class*="notation"]');
                    const svgContent = document.querySelectorAll('svg, img[src*=".svg"]');
                    
                    return scoreImages.length > 0 || musicNotation.length > 0 || svgContent.length > 0;
                }
            """
            )

            if has_score_content:
                print("✅ Score content detected - page loaded successfully")
                return True

            # Check if page seems to have loaded normally (not a redirect to login)
            current_url = page.url
            if (
                "login" in current_url
                or "signin" in current_url
                or "auth" in current_url
            ):
                print(f"🚨 Redirected to authentication page: {current_url}")
                return False

            # If title suggests this is a score page, assume it's OK even if we don't see content yet
            score_indicators = ["sheet music", "score", "musescore", "music"]
            if any(indicator in title_lower for indicator in score_indicators):
                print(f"✅ Score page detected by title: {title[:60]}...")
                return True

            print(f"⚠️  Unclear page state - proceeding with caution")
            return True  # Default to proceeding rather than blocking

        except Exception as e:
            print(f"⚠️ Error checking authentication: {e}")
            return True  # Default to proceeding

    async def comprehensive_scroll_and_extract(self, page):
        """More comprehensive scrolling and extraction"""
        print("🔍 Starting comprehensive score discovery...")

        svg_urls = set()

        # Initial check
        await self.find_score_images(page, svg_urls)
        print(f"📄 Initially visible: {len(svg_urls)} score images")

        # Get page dimensions
        viewport_height = await page.evaluate("window.innerHeight")
        page_height = await page.evaluate("document.body.scrollHeight")

        print(
            f"📐 Page dimensions: viewport={viewport_height}px, total={page_height}px"
        )

        # Scroll in smaller increments
        scroll_step = viewport_height // 4  # Quarter viewport steps
        current_y = 0

        while current_y < page_height:
            # Smooth scroll
            await page.evaluate(
                f"window.scrollTo({{top: {current_y}, behavior: 'smooth'}})"
            )
            await page.wait_for_timeout(2000)

            # Check for new images
            old_count = len(svg_urls)
            await self.find_score_images(page, svg_urls)

            if len(svg_urls) > old_count:
                new_found = len(svg_urls) - old_count
                print(f"✨ Discovered {new_found} new score(s) at y={current_y}")
                print(f"📊 Total found: {len(svg_urls)}")
                # Extra wait when finding new content
                await page.wait_for_timeout(3000)

            current_y += scroll_step

            # Update page height
            page_height = await page.evaluate("document.body.scrollHeight")

        # Final comprehensive scan
        print("🎯 Final comprehensive scan...")
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await page.wait_for_timeout(5000)

        await self.find_score_images(page, svg_urls)

        print(f"🎵 Total score images found: {len(svg_urls)}")

        # Sort by score number
        return self.sort_score_urls(svg_urls)

    async def find_score_images(self, page, svg_urls):
        """Find score images in current page state"""
        content = await page.content()
        soup = BeautifulSoup(content, "html.parser")

        for img in soup.find_all("img"):
            src = img.get("src", "")
            if src and "score_" in src and ".svg" in src:
                if src not in svg_urls:
                    score_num = self.extract_score_number(src)
                    print(f"   📄 Found: {score_num}")
                svg_urls.add(src)

    def sort_score_urls(self, svg_urls):
        """Sort score URLs by page number"""
        sorted_urls = []

        for url in svg_urls:
            match = re.search(r"score_(\d+)\.svg", url)
            if match:
                score_num = int(match.group(1))
                sorted_urls.append((score_num, url))

        sorted_urls.sort(key=lambda x: x[0])

        if sorted_urls:
            print("📋 Final sorted score list:")
            for score_num, url in sorted_urls:
                print(f"   score_{score_num}.svg")

        return [url for _, url in sorted_urls]

    def extract_score_number(self, url):
        """Extract score number for logging"""
        match = re.search(r"score_(\d+)\.svg", url)
        return f"score_{match.group(1)}.svg" if match else "unknown"

    async def convert_svg_to_image_with_browser(self, svg_url, output_path, page):
        """Convert SVG to image using browser screenshot"""
        try:
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ 
                        margin: 0; 
                        padding: 20px; 
                        background: white; 
                        display: flex;
                        justify-content: center;
                        align-items: flex-start;
                    }}
                    img {{ 
                        max-width: 95vw; 
                        height: auto; 
                        display: block; 
                    }}
                </style>
            </head>
            <body>
                <img src="{svg_url}" alt="Score" onload="window.imageLoaded = true;" onerror="window.imageError = true;" />
            </body>
            </html>
            """

            await page.set_content(html_content)

            # Wait for image to load
            try:
                await page.wait_for_function(
                    "window.imageLoaded === true || window.imageError === true",
                    timeout=20000,
                )
            except:
                return False

            # Check if loaded successfully
            image_loaded = await page.evaluate("window.imageLoaded === true")
            if not image_loaded:
                return False

            await page.wait_for_timeout(1000)

            # Screenshot
            img_element = await page.query_selector("img")
            if img_element:
                await img_element.screenshot(path=output_path, type="png")
                return True

            return False

        except Exception as e:
            return False

    def create_pdf_from_images(self, image_files, output_filename):
        """Create PDF from image files"""
        print(f"📄 Creating PDF: {output_filename}")

        c = canvas.Canvas(output_filename, pagesize=A4)
        width, height = A4

        for i, image_file in enumerate(image_files):
            if not os.path.exists(image_file):
                continue

            try:
                img = Image.open(image_file)
                img_width, img_height = img.size

                scale_x = (width - 40) / img_width
                scale_y = (height - 40) / img_height
                scale = min(scale_x, scale_y)

                final_width = img_width * scale
                final_height = img_height * scale
                x = (width - final_width) / 2
                y = (height - final_height) / 2

                c.drawImage(image_file, x, y, width=final_width, height=final_height)

                if i < len(image_files) - 1:
                    c.showPage()

                print(f"   ✅ Added page {i+1}: {os.path.basename(image_file)}")

            except Exception as e:
                continue

        c.save()
        print(f"✨ PDF saved as: {output_filename}")

    async def extract_and_convert(self, url, output_name):
        """Main extraction method"""
        browser = None
        try:
            Path(self.temp_dir).mkdir(exist_ok=True)

            async with async_playwright() as p:
                browser, context = await self.setup_browser_context(p)
                page = await context.new_page()

                print(f"🌐 Loading: {url}")

                # More flexible loading
                try:
                    await page.goto(url, wait_until="domcontentloaded", timeout=30000)
                    await page.wait_for_timeout(5000)  # Give it time to load
                except:
                    print("⚠️ Slow loading, but continuing...")

                # Smarter auth check
                if not await self.smart_auth_check(page):
                    print("🚨 Authentication required - cannot proceed")
                    return False

                # Extract SVGs
                svg_urls = await self.comprehensive_scroll_and_extract(page)

                if not svg_urls:
                    print("❌ No score images found!")
                    return False

                print(f"🎯 Converting {len(svg_urls)} score pages...")

                # Convert to images
                image_files = []
                for i, svg_url in enumerate(svg_urls):
                    image_filename = os.path.join(self.temp_dir, f"score_{i}.png")
                    score_name = self.extract_score_number(svg_url)
                    print(f"🖼️  Converting {score_name}...")

                    if await self.convert_svg_to_image_with_browser(
                        svg_url, image_filename, page
                    ):
                        image_files.append(image_filename)
                        print(f"   ✅ Success")
                    else:
                        print(f"   ❌ Failed")

                if not image_files:
                    print("❌ No images created successfully!")
                    return False

                # Create PDF
                pdf_filename = f"{output_name}.pdf"
                self.create_pdf_from_images(image_files, pdf_filename)

                return True

        except Exception as e:
            print(f"❌ Error: {e}")
            return False

        finally:
            if browser:
                await browser.close()

            # Cleanup
            for file in Path(self.temp_dir).glob("*.png"):
                try:
                    file.unlink()
                except:
                    pass
            try:
                Path(self.temp_dir).rmdir()
            except:
                pass


async def main():
    extractor = FixedPlaywrightExtractor()

    url = input("Enter the URL: ").strip()
    output_name = input("Enter the output PDF name (without .pdf extension): ").strip()

    if not url or not output_name:
        print("Please provide both URL and output name!")
        return

    print("🚀 Starting fixed extraction...")
    success = await extractor.extract_and_convert(url, output_name)

    if success:
        print(f"🎉 Successfully created {output_name}.pdf!")
    else:
        print("💥 Failed to create PDF. Check the error messages above.")


if __name__ == "__main__":
    asyncio.run(main())
