import asyncio
import re
import sys
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from PIL import Image
import io
import os
from pathlib import Path

try:
    import colorama
    from colorama import Fore, Back, Style

    colorama.init()
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False

# Application branding
APP_NAME = "Oops I Stole Your Sheet - By TheMentor - Built for LoverButtMunchkin"
APP_VERSION = "1.0"


def console_print(message, color=None, style=None):
    """Enhanced console printing with colors if available"""
    if COLORAMA_AVAILABLE and color:
        color_map = {
            "red": Fore.RED,
            "green": Fore.GREEN,
            "yellow": Fore.YELLOW,
            "blue": Fore.BLUE,
            "magenta": Fore.MAGENTA,
            "cyan": <PERSON><PERSON>.<PERSON>,
            "white": Fore.WHITE,
        }
        style_map = {"bright": Style.BRIGHT, "dim": Style.DIM}

        prefix = ""
        if color in color_map:
            prefix += color_map[color]
        if style in style_map:
            prefix += style_map[style]

        print(f"{prefix}{message}{Style.RESET_ALL}")
    else:
        print(message)


def setup_playwright_browsers():
    """Setup Playwright browsers for executable"""
    if getattr(sys, "frozen", False):
        # Running as executable
        exe_dir = Path(sys.executable).parent

        # Set up browser paths for bundled browsers
        bundled_browsers = exe_dir / "_internal" / "ms-playwright"
        if bundled_browsers.exists():
            os.environ["PLAYWRIGHT_BROWSERS_PATH"] = str(bundled_browsers)
            console_print(f"Using bundled browsers at: {bundled_browsers}", "green")
            return True
        else:
            console_print(
                "Bundled browsers not found, using system installation...", "yellow"
            )

    return False


def check_and_install_browsers():
    """Check if browsers are available and offer to install if not"""
    try:
        from playwright.sync_api import sync_playwright

        with sync_playwright() as p:
            # Try to get browser executable path
            browser_path = p.chromium.executable_path
            if browser_path and Path(browser_path).exists():
                return True
            else:
                raise Exception("Browser not found")

    except Exception:
        console_print("🌐 Playwright browsers not found!", "yellow")
        console_print("This is required for the application to work.", "yellow")

        if getattr(sys, "frozen", False):
            # Running as executable
            console_print("Please run the following command in a terminal:", "cyan")
            console_print("playwright install chromium", "white", "bright")
            console_print("\nOr download browsers automatically? (y/n): ", "cyan")

            try:
                choice = input().strip().lower()
                if choice in ["y", "yes"]:
                    return install_browsers_subprocess()
            except:
                pass
        else:
            # Running from source
            console_print("Please run: playwright install chromium", "cyan")

        return False


def install_browsers_subprocess():
    """Install browsers using subprocess"""
    try:
        import subprocess

        console_print("📥 Installing Playwright browsers...", "cyan")
        console_print("This may take a few minutes...", "yellow")

        result = subprocess.run(
            [sys.executable, "-m", "playwright", "install", "chromium"],
            capture_output=True,
            text=True,
            timeout=300,
        )

        if result.returncode == 0:
            console_print("✅ Browsers installed successfully!", "green")
            return True
        else:
            console_print(f"❌ Failed to install browsers: {result.stderr}", "red")
            return False

    except Exception as e:
        console_print(f"❌ Error installing browsers: {e}", "red")
        return False


class FixedPlaywrightExtractor:
    def __init__(self, progress_callback=None, log_callback=None):
        self.temp_dir = "temp_images"
        self.progress_callback = progress_callback
        self.log_callback = log_callback

    def log(self, message, color=None, style=None):
        """Log message to both console and GUI if available"""
        if self.log_callback:
            self.log_callback(message)
        else:
            console_print(message, color, style)

    def update_progress(self, value, text=""):
        """Update progress bar if available"""
        if self.progress_callback:
            self.progress_callback(value, text)

    async def setup_browser_context(self, p):
        """Setup browser with anti-detection measures"""
        browser = await p.chromium.launch(
            headless=False,  # Keep visible for debugging
            args=[
                "--disable-blink-features=AutomationControlled",
                "--disable-web-security",
                "--no-sandbox",
                "--disable-dev-shm-usage",
            ],
        )

        context = await browser.new_context(
            viewport={"width": 1920, "height": 1080},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        )

        # Remove automation indicators
        await context.add_init_script(
            """
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """
        )

        return browser, context

    async def smart_auth_check(self, page):
        """Smarter authentication detection - only flag actual auth barriers"""
        try:
            # Check for actual authentication barriers, not just login links
            title = await page.title()

            # These indicate actual authentication requirements
            auth_barriers = [
                "sign in to continue",
                "login required",
                "access denied",
                "unauthorized access",
                "authentication required",
                "please log in",
                "you must be logged in",
            ]

            title_lower = title.lower()

            for barrier in auth_barriers:
                if barrier in title_lower:
                    self.log(
                        f"🚨 Authentication barrier detected: '{barrier}' in title",
                        "red",
                    )
                    return False

            # Check if we can see actual score content
            has_score_content = await page.evaluate(
                """
                () => {
                    // Look for score-related content
                    const scoreImages = document.querySelectorAll('img[src*="score_"]');
                    const musicNotation = document.querySelectorAll('.score, [class*="score"], [class*="notation"]');
                    const svgContent = document.querySelectorAll('svg, img[src*=".svg"]');

                    return scoreImages.length > 0 || musicNotation.length > 0 || svgContent.length > 0;
                }
            """
            )

            if has_score_content:
                self.log(
                    "✅ Score content detected - page loaded successfully", "green"
                )
                return True

            # Check if page seems to have loaded normally (not a redirect to login)
            current_url = page.url
            if (
                "login" in current_url
                or "signin" in current_url
                or "auth" in current_url
            ):
                self.log(f"🚨 Redirected to authentication page: {current_url}", "red")
                return False

            # If title suggests this is a score page, assume it's OK even if we don't see content yet
            score_indicators = ["sheet music", "score", "musescore", "music"]
            if any(indicator in title_lower for indicator in score_indicators):
                self.log(f"✅ Score page detected by title: {title[:60]}...", "green")
                return True

            self.log(f"⚠️  Unclear page state - proceeding with caution", "yellow")
            return True  # Default to proceeding rather than blocking

        except Exception as e:
            self.log(f"⚠️ Error checking authentication: {e}", "yellow")
            return True  # Default to proceeding

    async def comprehensive_scroll_and_extract(self, page):
        """More comprehensive scrolling and extraction"""
        self.log("🔍 Starting comprehensive score discovery...", "cyan")
        self.update_progress(10, "Discovering score images...")

        svg_urls = set()

        # Initial check
        await self.find_score_images(page, svg_urls)
        self.log(f"📄 Initially visible: {len(svg_urls)} score images", "blue")

        # Get page dimensions
        viewport_height = await page.evaluate("window.innerHeight")
        page_height = await page.evaluate("document.body.scrollHeight")

        self.log(
            f"📐 Page dimensions: viewport={viewport_height}px, total={page_height}px",
            "blue",
        )

        # Scroll in smaller increments
        scroll_step = viewport_height // 4  # Quarter viewport steps
        current_y = 0

        while current_y < page_height:
            # Update progress based on scroll position
            progress = 10 + (current_y / page_height) * 40  # 10-50% for scrolling
            self.update_progress(progress, f"Scrolling... {current_y}/{page_height}px")

            # Smooth scroll
            await page.evaluate(
                f"window.scrollTo({{top: {current_y}, behavior: 'smooth'}})"
            )
            await page.wait_for_timeout(2000)

            # Check for new images
            old_count = len(svg_urls)
            await self.find_score_images(page, svg_urls)

            if len(svg_urls) > old_count:
                new_found = len(svg_urls) - old_count
                self.log(
                    f"✨ Discovered {new_found} new score(s) at y={current_y}", "green"
                )
                self.log(f"📊 Total found: {len(svg_urls)}", "green")
                # Extra wait when finding new content
                await page.wait_for_timeout(3000)

            current_y += scroll_step

            # Update page height
            page_height = await page.evaluate("document.body.scrollHeight")

        # Final comprehensive scan
        self.log("🎯 Final comprehensive scan...", "cyan")
        self.update_progress(50, "Final scan...")
        await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
        await page.wait_for_timeout(5000)

        await self.find_score_images(page, svg_urls)

        self.log(f"🎵 Total score images found: {len(svg_urls)}", "green", "bright")

        # Sort by score number
        return self.sort_score_urls(svg_urls)

    async def find_score_images(self, page, svg_urls):
        """Find score images in current page state"""
        content = await page.content()
        soup = BeautifulSoup(content, "html.parser")

        for img in soup.find_all("img"):
            src = img.get("src", "")
            if src and "score_" in src and ".svg" in src:
                if src not in svg_urls:
                    score_num = self.extract_score_number(src)
                    self.log(f"   📄 Found: {score_num}", "blue")
                svg_urls.add(src)

    def sort_score_urls(self, svg_urls):
        """Sort score URLs by page number"""
        sorted_urls = []

        for url in svg_urls:
            match = re.search(r"score_(\d+)\.svg", url)
            if match:
                score_num = int(match.group(1))
                sorted_urls.append((score_num, url))

        sorted_urls.sort(key=lambda x: x[0])

        if sorted_urls:
            self.log("📋 Final sorted score list:", "cyan")
            for score_num, url in sorted_urls:
                self.log(f"   score_{score_num}.svg", "blue")

        return [url for _, url in sorted_urls]

    def extract_score_number(self, url):
        """Extract score number for logging"""
        match = re.search(r"score_(\d+)\.svg", url)
        return f"score_{match.group(1)}.svg" if match else "unknown"

    async def convert_svg_to_image_with_browser(self, svg_url, output_path, page):
        """Convert SVG to image using browser screenshot"""
        try:
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <style>
                    body {{ 
                        margin: 0; 
                        padding: 20px; 
                        background: white; 
                        display: flex;
                        justify-content: center;
                        align-items: flex-start;
                    }}
                    img {{ 
                        max-width: 95vw; 
                        height: auto; 
                        display: block; 
                    }}
                </style>
            </head>
            <body>
                <img src="{svg_url}" alt="Score" onload="window.imageLoaded = true;" onerror="window.imageError = true;" />
            </body>
            </html>
            """

            await page.set_content(html_content)

            # Wait for image to load
            try:
                await page.wait_for_function(
                    "window.imageLoaded === true || window.imageError === true",
                    timeout=20000,
                )
            except:
                return False

            # Check if loaded successfully
            image_loaded = await page.evaluate("window.imageLoaded === true")
            if not image_loaded:
                return False

            await page.wait_for_timeout(1000)

            # Screenshot
            img_element = await page.query_selector("img")
            if img_element:
                await img_element.screenshot(path=output_path, type="png")
                return True

            return False

        except Exception as e:
            return False

    def create_pdf_from_images(self, image_files, output_filename):
        """Create PDF from image files"""
        self.log(f"📄 Creating PDF: {output_filename}", "cyan")
        self.update_progress(80, "Creating PDF...")

        c = canvas.Canvas(output_filename, pagesize=A4)
        width, height = A4

        for i, image_file in enumerate(image_files):
            if not os.path.exists(image_file):
                continue

            try:
                # Update progress for each page
                progress = 80 + (i / len(image_files)) * 15  # 80-95% for PDF creation
                self.update_progress(
                    progress, f"Adding page {i+1}/{len(image_files)} to PDF..."
                )

                img = Image.open(image_file)
                img_width, img_height = img.size

                scale_x = (width - 40) / img_width
                scale_y = (height - 40) / img_height
                scale = min(scale_x, scale_y)

                final_width = img_width * scale
                final_height = img_height * scale
                x = (width - final_width) / 2
                y = (height - final_height) / 2

                c.drawImage(image_file, x, y, width=final_width, height=final_height)

                if i < len(image_files) - 1:
                    c.showPage()

                self.log(
                    f"   ✅ Added page {i+1}: {os.path.basename(image_file)}", "green"
                )

            except Exception:
                continue

        c.save()
        self.update_progress(95, "PDF creation complete!")
        self.log(f"✨ PDF saved as: {output_filename}", "green", "bright")

    async def extract_and_convert(self, url, output_name):
        """Main extraction method"""
        browser = None
        try:
            self.update_progress(0, "Initializing...")
            Path(self.temp_dir).mkdir(exist_ok=True)

            async with async_playwright() as p:
                browser, context = await self.setup_browser_context(p)
                page = await context.new_page()

                self.log(f"🌐 Loading: {url}", "cyan")
                self.update_progress(5, "Loading page...")

                # More flexible loading
                try:
                    await page.goto(url, wait_until="domcontentloaded", timeout=30000)
                    await page.wait_for_timeout(5000)  # Give it time to load
                except:
                    self.log("⚠️ Slow loading, but continuing...", "yellow")

                # Smarter auth check
                if not await self.smart_auth_check(page):
                    self.log("🚨 Authentication required - cannot proceed", "red")
                    return False

                # Extract SVGs
                svg_urls = await self.comprehensive_scroll_and_extract(page)

                if not svg_urls:
                    self.log("❌ No score images found!", "red")
                    return False

                self.log(f"🎯 Converting {len(svg_urls)} score pages...", "cyan")
                self.update_progress(55, "Converting images...")

                # Convert to images
                image_files = []
                for i, svg_url in enumerate(svg_urls):
                    # Update progress for each conversion
                    progress = 55 + (i / len(svg_urls)) * 25  # 55-80% for conversions
                    image_filename = os.path.join(self.temp_dir, f"score_{i}.png")
                    score_name = self.extract_score_number(svg_url)
                    self.update_progress(progress, f"Converting {score_name}...")
                    self.log(f"🖼️  Converting {score_name}...", "blue")

                    if await self.convert_svg_to_image_with_browser(
                        svg_url, image_filename, page
                    ):
                        image_files.append(image_filename)
                        self.log(f"   ✅ Success", "green")
                    else:
                        self.log(f"   ❌ Failed", "red")

                if not image_files:
                    self.log("❌ No images created successfully!", "red")
                    return False

                # Create PDF
                pdf_filename = f"{output_name}.pdf"
                self.create_pdf_from_images(image_files, pdf_filename)
                self.update_progress(100, "Complete!")

                return True

        except Exception as e:
            self.log(f"❌ Error: {e}", "red")
            return False

        finally:
            if browser:
                await browser.close()

            # Cleanup
            for file in Path(self.temp_dir).glob("*.png"):
                try:
                    file.unlink()
                except:
                    pass
            try:
                Path(self.temp_dir).rmdir()
            except:
                pass


class SheetMusicExtractorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(APP_NAME)
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        # Configure style
        self.setup_styles()

        # Variables
        self.url_var = tk.StringVar()
        self.output_var = tk.StringVar()
        self.is_running = False

        # Create GUI
        self.create_widgets()

        # Center window
        self.center_window()

    def setup_styles(self):
        """Setup modern styling"""
        style = ttk.Style()

        # Configure colors
        bg_color = "#2b2b2b"
        fg_color = "#ffffff"
        accent_color = "#4a9eff"

        self.root.configure(bg=bg_color)

        # Configure ttk styles
        style.theme_use("clam")
        style.configure(
            "Title.TLabel",
            background=bg_color,
            foreground=accent_color,
            font=("Arial", 16, "bold"),
        )
        style.configure(
            "Subtitle.TLabel",
            background=bg_color,
            foreground=fg_color,
            font=("Arial", 10),
        )
        style.configure(
            "Modern.TEntry",
            fieldbackground="#404040",
            foreground=fg_color,
            borderwidth=1,
            relief="solid",
        )
        style.configure(
            "Modern.TButton",
            background=accent_color,
            foreground="white",
            borderwidth=0,
            focuscolor="none",
        )
        style.map("Modern.TButton", background=[("active", "#3a8eef")])

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """Create and layout GUI widgets"""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text=APP_NAME, style="Title.TLabel")
        title_label.pack(pady=(0, 10))

        version_label = ttk.Label(
            main_frame, text=f"Version {APP_VERSION}", style="Subtitle.TLabel"
        )
        version_label.pack(pady=(0, 20))

        # URL input
        url_frame = ttk.Frame(main_frame)
        url_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(url_frame, text="Sheet Music URL:", style="Subtitle.TLabel").pack(
            anchor=tk.W
        )
        self.url_entry = ttk.Entry(
            url_frame,
            textvariable=self.url_var,
            style="Modern.TEntry",
            font=("Arial", 10),
        )
        self.url_entry.pack(fill=tk.X, pady=(5, 0))

        # Output name input
        output_frame = ttk.Frame(main_frame)
        output_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(
            output_frame,
            text="Output PDF Name (without .pdf):",
            style="Subtitle.TLabel",
        ).pack(anchor=tk.W)

        output_input_frame = ttk.Frame(output_frame)
        output_input_frame.pack(fill=tk.X, pady=(5, 0))

        self.output_entry = ttk.Entry(
            output_input_frame,
            textvariable=self.output_var,
            style="Modern.TEntry",
            font=("Arial", 10),
        )
        self.output_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        browse_btn = ttk.Button(
            output_input_frame,
            text="Browse",
            style="Modern.TButton",
            command=self.browse_output,
        )
        browse_btn.pack(side=tk.RIGHT)

        # Progress section
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        self.progress_var = tk.StringVar(value="Ready to extract sheet music")
        self.progress_label = ttk.Label(
            progress_frame, textvariable=self.progress_var, style="Subtitle.TLabel"
        )
        self.progress_label.pack(anchor=tk.W)

        self.progress_bar = ttk.Progressbar(
            progress_frame, mode="determinate", length=400
        )
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(15, 0))

        self.extract_btn = ttk.Button(
            button_frame,
            text="Extract Sheet Music",
            style="Modern.TButton",
            command=self.start_extraction,
        )
        self.extract_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_btn = ttk.Button(
            button_frame,
            text="Stop",
            style="Modern.TButton",
            command=self.stop_extraction,
            state=tk.DISABLED,
        )
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))

        clear_btn = ttk.Button(
            button_frame,
            text="Clear",
            style="Modern.TButton",
            command=self.clear_fields,
        )
        clear_btn.pack(side=tk.LEFT)

        # Log area
        log_frame = ttk.Frame(main_frame)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))

        ttk.Label(log_frame, text="Log:", style="Subtitle.TLabel").pack(anchor=tk.W)

        # Text widget with scrollbar
        text_frame = ttk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        self.log_text = tk.Text(
            text_frame, bg="#1e1e1e", fg="#ffffff", font=("Consolas", 9), wrap=tk.WORD
        )
        scrollbar = ttk.Scrollbar(
            text_frame, orient=tk.VERTICAL, command=self.log_text.yview
        )
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def browse_output(self):
        """Browse for output file location"""
        filename = filedialog.asksaveasfilename(
            title="Save PDF as...",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
        )
        if filename:
            # Remove .pdf extension if present
            if filename.endswith(".pdf"):
                filename = filename[:-4]
            self.output_var.set(filename)

    def clear_fields(self):
        """Clear all input fields and log"""
        self.url_var.set("")
        self.output_var.set("")
        self.log_text.delete(1.0, tk.END)
        self.progress_bar["value"] = 0
        self.progress_var.set("Ready to extract sheet music")

    def log_message(self, message):
        """Add message to log area"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_progress_gui(self, value, text=""):
        """Update progress bar and text"""
        self.progress_bar["value"] = value
        if text:
            self.progress_var.set(text)
        self.root.update_idletasks()

    def start_extraction(self):
        """Start the extraction process in a separate thread"""
        if self.is_running:
            return

        url = self.url_var.get().strip()
        output_name = self.output_var.get().strip()

        # Input validation
        if not url:
            messagebox.showerror("Error", "Please enter a URL")
            self.url_entry.focus()
            return

        if not output_name:
            messagebox.showerror("Error", "Please enter an output name")
            self.output_entry.focus()
            return

        # Basic URL validation
        if not (url.startswith("http://") or url.startswith("https://")):
            result = messagebox.askyesno(
                "Invalid URL",
                "The URL doesn't start with http:// or https://. Do you want to add https:// automatically?",
            )
            if result:
                url = "https://" + url
                self.url_var.set(url)
            else:
                self.url_entry.focus()
                return

        # Validate output name (remove invalid characters)
        import re

        invalid_chars = r'[<>:"/\\|?*]'
        if re.search(invalid_chars, output_name):
            messagebox.showerror(
                "Error",
                'Output name contains invalid characters. Please remove: < > : " / \\ | ? *',
            )
            self.output_entry.focus()
            return

        self.is_running = True
        self.extract_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)

        # Clear log
        self.log_text.delete(1.0, tk.END)

        # Start extraction in thread
        self.extraction_thread = threading.Thread(
            target=self.run_extraction_async, args=(url, output_name), daemon=True
        )
        self.extraction_thread.start()

    def run_extraction_async(self, url, output_name):
        """Run the async extraction in a thread"""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Create extractor with callbacks
            extractor = FixedPlaywrightExtractor(
                progress_callback=self.update_progress_gui,
                log_callback=self.log_message,
            )

            # Run extraction
            success = loop.run_until_complete(
                extractor.extract_and_convert(url, output_name)
            )

            # Update UI on completion
            self.root.after(0, self.extraction_complete, success, output_name)

        except Exception as e:
            self.root.after(0, self.extraction_error, str(e))
        finally:
            loop.close()

    def extraction_complete(self, success, output_name):
        """Handle extraction completion"""
        self.is_running = False
        self.extract_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

        if success:
            self.log_message(f"🎉 Successfully created {output_name}.pdf!")
            self.progress_var.set("Extraction completed successfully!")
            messagebox.showinfo(
                "Success", f"PDF created successfully!\n\nSaved as: {output_name}.pdf"
            )
        else:
            self.log_message("💥 Failed to create PDF. Check the error messages above.")
            self.progress_var.set("Extraction failed")
            messagebox.showerror(
                "Error",
                "Failed to extract sheet music. Please check the log for details.",
            )

    def extraction_error(self, error_msg):
        """Handle extraction error"""
        self.is_running = False
        self.extract_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

        self.log_message(f"💥 Error: {error_msg}")
        self.progress_var.set("Error occurred")
        messagebox.showerror("Error", f"An error occurred:\n{error_msg}")

    def stop_extraction(self):
        """Stop the extraction process"""
        # Note: This is a simple implementation. For a more robust solution,
        # you'd need to implement proper cancellation in the async code
        self.is_running = False
        self.extract_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.progress_var.set("Extraction stopped")
        self.log_message("⏹️ Extraction stopped by user")

    def run(self):
        """Start the GUI main loop"""
        self.root.mainloop()


async def console_main():
    """Console mode main function"""
    console_print(f"\n{APP_NAME}", "cyan", "bright")
    console_print(f"Version {APP_VERSION}\n", "blue")

    extractor = FixedPlaywrightExtractor()

    # Get URL with validation
    while True:
        url = input("Enter the URL: ").strip()
        if not url:
            console_print("Please enter a URL!", "red")
            continue

        # Basic URL validation
        if not (url.startswith("http://") or url.startswith("https://")):
            console_print("URL should start with http:// or https://", "yellow")
            add_https = input("Add https:// automatically? (y/n): ").strip().lower()
            if add_https in ["y", "yes"]:
                url = "https://" + url
                break
            else:
                continue
        break

    # Get output name with validation
    while True:
        output_name = input(
            "Enter the output PDF name (without .pdf extension): "
        ).strip()
        if not output_name:
            console_print("Please enter an output name!", "red")
            continue

        # Validate output name
        import re

        invalid_chars = r'[<>:"/\\|?*]'
        if re.search(invalid_chars, output_name):
            console_print(
                'Output name contains invalid characters: < > : " / \\ | ? *', "red"
            )
            continue
        break

    console_print("🚀 Starting extraction...", "cyan", "bright")

    try:
        success = await extractor.extract_and_convert(url, output_name)

        if success:
            console_print(
                f"🎉 Successfully created {output_name}.pdf!", "green", "bright"
            )
            console_print("Press Enter to exit...", "blue")
            input()
        else:
            console_print(
                "💥 Failed to create PDF. Check the error messages above.", "red"
            )
            console_print("Press Enter to exit...", "blue")
            input()
    except KeyboardInterrupt:
        console_print("\n⏹️ Extraction cancelled by user", "yellow")
    except Exception as e:
        console_print(f"💥 Unexpected error: {e}", "red")
        console_print("Press Enter to exit...", "blue")
        input()


def main():
    """Main entry point - choose between GUI and console mode"""
    # Setup Playwright browsers for executable
    setup_playwright_browsers()

    # Check if browsers are available
    if not check_and_install_browsers():
        console_print("❌ Cannot proceed without Playwright browsers.", "red")
        console_print("Please install browsers and try again.", "yellow")
        input("Press Enter to exit...")
        return

    if len(sys.argv) > 1 and sys.argv[1] in ["--console", "-c", "--cli"]:
        # Console mode
        asyncio.run(console_main())
    else:
        # GUI mode (default)
        try:
            app = SheetMusicExtractorGUI()
            app.run()
        except Exception as e:
            console_print(f"Failed to start GUI: {e}", "red")
            console_print("Falling back to console mode...", "yellow")
            asyncio.run(console_main())


if __name__ == "__main__":
    main()
